# Firebase Setup Guide for edu_app

## Current Status: ✅ Partially Complete

### What's Done:
- ✅ Firebase dependencies added to pubspec.yaml
- ✅ Android build configuration updated
- ✅ Firebase initialization code added to main.dart
- ✅ Firebase test screen created
- ✅ Basic firebase_options.dart template created

### What You Need to Do:

#### 1. Complete Firebase Console Setup

**Go to:** https://console.firebase.google.com/project/edu-app-1ed3b/overview

**For Android:**
1. Click "Add app" → Select Android
2. Package name: `com.example.edu_app`
3. App nickname: `edu_app` (optional)
4. Download `google-services.json`
5. **IMPORTANT:** Place `google-services.json` in `android/app/` directory

**For iOS (optional):**
1. Click "Add app" → Select iOS
2. Bundle ID: `com.example.eduApp`
3. Download `GoogleService-Info.plist`
4. Place in `ios/Runner/` directory

**For Web (optional):**
1. Click "Add app" → Select Web
2. App nickname: `edu_app_web`
3. Copy the configuration object

#### 2. Update firebase_options.dart

Replace the placeholder values in `lib/firebase_options.dart` with real values from Firebase Console:

```dart
// Replace these placeholders:
'YOUR_WEB_API_KEY' → actual web API key
'YOUR_ANDROID_API_KEY' → actual Android API key
'YOUR_IOS_API_KEY' → actual iOS API key
'YOUR_WEB_APP_ID' → actual web app ID
'YOUR_ANDROID_APP_ID' → actual Android app ID
'YOUR_IOS_APP_ID' → actual iOS app ID
'YOUR_MESSAGING_SENDER_ID' → actual messaging sender ID
```

#### 3. Test the Setup

1. Run: `flutter run`
2. Navigate to "Firebase" tab in the app
3. Should see green checkmark if successful

#### 4. Enable Firebase Services (as needed)

In Firebase Console, enable:
- **Authentication** (for user login)
- **Firestore Database** (for data storage)
- **Storage** (for file uploads)

### Available Firebase Services in Your App:

```dart
// Authentication
import 'package:firebase_auth/firebase_auth.dart';

// Firestore Database
import 'package:cloud_firestore/cloud_firestore.dart';

// Storage
import 'package:firebase_storage/firebase_storage.dart';
```

### Common Next Steps:

1. **Set up Authentication:**
   - Email/password login
   - Google Sign-In
   - Phone authentication

2. **Set up Firestore:**
   - User profiles
   - Chat messages
   - Marketplace listings

3. **Set up Storage:**
   - Profile pictures
   - File attachments

### Troubleshooting:

- If you see "Firebase Error" in the test screen, check that `google-services.json` is in the correct location
- Make sure all API keys in `firebase_options.dart` are correct
- Run `flutter clean` and `flutter pub get` if you encounter build issues

### Need Help?

If you encounter issues, check:
1. Firebase Console project settings
2. `google-services.json` file location
3. API keys in `firebase_options.dart`
4. Android build configuration
