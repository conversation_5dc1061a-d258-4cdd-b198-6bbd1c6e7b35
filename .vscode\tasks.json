{"version": "2.0.0", "tasks": [{"label": "Flutter: Clean", "type": "shell", "command": "flutter", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Get Dependencies", "type": "shell", "command": "flutter", "args": ["pub", "get"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Build APK", "type": "shell", "command": "flutter", "args": ["build", "apk", "--release"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Build Web", "type": "shell", "command": "flutter", "args": ["build", "web", "--release"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Flutter: Run Tests", "type": "shell", "command": "flutter", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}