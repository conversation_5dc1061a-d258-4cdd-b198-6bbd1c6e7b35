{"dart.flutterSdkPath": null, "dart.debugExternalPackageLibraries": false, "dart.debugSdkLibraries": false, "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "dart.flutterCreateAndroidLanguage": "kotlin", "dart.flutterCreateIOSLanguage": "swift", "dart.flutterCreatePlatforms": ["android", "web"], "dart.flutterCreateOrganization": "com.example", "files.associations": {"*.dart": "dart"}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "dart.lineLength": 120, "dart.insertArgumentPlaceholders": false, "dart.enableSdkFormatter": true, "dart.runPubGetOnPubspecChanges": true, "dart.warnWhenEditingFilesOutsideWorkspace": true, "dart.allowAnalytics": false, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/build": true, "**/.dart_tool": true}, "search.exclude": {"**/build": true, "**/.dart_tool": true, "**/node_modules": true}}