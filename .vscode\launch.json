{"version": "0.2.0", "configurations": [{"name": "IlmExchange (Debug)", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["--flavor", "development"]}, {"name": "IlmExchange (Release)", "request": "launch", "type": "dart", "program": "lib/main.dart", "flutterMode": "release"}, {"name": "IlmExchange (Profile)", "request": "launch", "type": "dart", "program": "lib/main.dart", "flutterMode": "profile"}, {"name": "IlmExchange (Web)", "request": "launch", "type": "dart", "program": "lib/main.dart", "deviceId": "chrome", "args": ["--web-renderer", "html"]}, {"name": "IlmExchange (Android)", "request": "launch", "type": "dart", "program": "lib/main.dart", "deviceId": "android"}]}