# 🔥 Firestore Setup & Troubleshooting Guide

## 🚨 **Current Issue: Permission Denied**

Your app is getting a "Missing or insufficient permissions" error when trying to write to Firestore. This is because Firestore security rules are blocking the operation.

## ✅ **Quick Fix - Enable Test Mode (Temporary)**

### **Step 1: Go to Firebase Console**
1. Open: https://console.firebase.google.com/project/edu-app-1ed3b/firestore
2. Click on **"Rules"** tab
3. Replace the current rules with this **temporary** test rule:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // WARNING: These rules allow anyone to read/write. Use only for testing!
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
```

4. Click **"Publish"**

### **Step 2: Test Your App**
- Try posting a skill offer/request
- Should work immediately after publishing rules

---

## 🔒 **Production-Ready Security Rules**

After testing, replace with these secure rules:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Skills collection - authenticated users can read all, write their own
    match /skills/{skillId} {
      allow read: if true; // Anyone can read skills
      allow create: if request.auth != null 
        && request.auth.uid == request.resource.data.userId;
      allow update, delete: if request.auth != null 
        && request.auth.uid == resource.data.userId;
    }
    
    // Users collection - users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Chat messages - authenticated users only
    match /chats/{chatId} {
      allow read, write: if request.auth != null;
    }
  }
}
```

---

## 🛠️ **Alternative Solutions**

### **Option 1: Anonymous Authentication (Quick Test)**
Add this to your `_submitToFirestore` method:

```dart
// Sign in anonymously if no user
if (user == null) {
  final credential = await FirebaseAuth.instance.signInAnonymously();
  user = credential.user;
}
```

### **Option 2: Force User Login**
Ensure users are always logged in before accessing the Post screen.

---

## 🧪 **Testing Firestore Connection**

Add this test function to your app:

```dart
Future<void> testFirestoreConnection() async {
  try {
    final testDoc = await FirebaseFirestore.instance
        .collection('test')
        .add({'message': 'Hello Firestore!', 'timestamp': FieldValue.serverTimestamp()});
    
    print('✅ Firestore test successful: ${testDoc.id}');
    
    // Clean up test document
    await testDoc.delete();
    print('✅ Test document cleaned up');
    
  } catch (e) {
    print('❌ Firestore test failed: $e');
  }
}
```

---

## 🔍 **Debugging Steps**

### **1. Check Firebase Console**
- Go to: https://console.firebase.google.com/project/edu-app-1ed3b/firestore/data
- Verify the database exists
- Check if any documents are being created

### **2. Check Browser Console**
- Open Chrome DevTools (F12)
- Look for Firebase/Firestore errors
- Check Network tab for failed requests

### **3. Verify Authentication**
```dart
final user = FirebaseAuth.instance.currentUser;
print('Current user: ${user?.uid} (${user?.email})');
print('Is anonymous: ${user?.isAnonymous}');
```

---

## 📋 **Checklist**

- [ ] Firebase project exists: `edu-app-1ed3b`
- [ ] Firestore database is created
- [ ] Security rules are published
- [ ] User is authenticated (or anonymous auth enabled)
- [ ] Internet connection is stable
- [ ] No browser blocking Firebase requests

---

## 🆘 **Still Having Issues?**

### **Common Error Messages:**

**"Missing or insufficient permissions"**
→ Fix security rules (see Step 1 above)

**"PERMISSION_DENIED"**
→ User not authenticated or rules too restrictive

**"UNAVAILABLE"**
→ Network issue or Firestore is down

**"DEADLINE_EXCEEDED"**
→ Request timeout, check internet connection

---

## 🎯 **Next Steps After Fix**

1. ✅ Test posting skills (both offers and requests)
2. ✅ Verify data appears in Firebase Console
3. ✅ Test with different users
4. ✅ Implement proper security rules
5. ✅ Add offline support if needed

---

**Need immediate help?** 
- Check the browser console for specific error messages
- Try the test mode rules first (Step 1)
- Verify your internet connection
- Make sure you're logged into the correct Firebase project
