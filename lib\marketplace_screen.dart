import 'package:flutter/material.dart';

class MarketplaceScreen extends StatefulWidget {
  const MarketplaceScreen({super.key});

  @override
  State<MarketplaceScreen> createState() => _MarketplaceScreenState();
}

class _MarketplaceScreenState extends State<MarketplaceScreen> {
  bool showOffers = true;
  String searchQuery = '';

  final List<Map<String, String>> offers = [
    {'name': '<PERSON><PERSON>', 'skill': 'Graphic Design', 'desc': 'Can teach Figma basics.'},
    {'name': 'Zain', 'skill': 'Guitar', 'desc': 'Beginner and intermediate levels.'},
  ];

  final List<Map<String, String>> requests = [
    {'name': 'Fatima', 'skill': 'Python', 'desc': 'Wants help with OOP concepts.'},
    {'name': 'Ali', 'skill': 'English Speaking', 'desc': 'Needs fluency practice.'},
  ];

  List<Map<String, String>> getFilteredList() {
    List<Map<String, String>> list = showOffers ? offers : requests;
    if (searchQuery.isEmpty) return list;
    return list.where((item) =>
      item['skill']!.toLowerCase().contains(searchQuery.toLowerCase()) ||
      item['name']!.toLowerCase().contains(searchQuery.toLowerCase())
    ).toList();
  }

  Widget _buildSkillCard(Map<String, String> data) {
    return Card(
      elevation: 3,
      margin: EdgeInsets.symmetric(vertical: 8, horizontal: 10),
      child: ListTile(
        leading: CircleAvatar(child: Icon(Icons.person)),
        title: Text(data['name']!),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text("Skill: ${data['skill']}"),
            SizedBox(height: 4),
            Text(data['desc']!, style: TextStyle(fontSize: 12)),
          ],
        ),
        trailing: ElevatedButton(
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text("Feature Coming Soon!")),
            );
          },
          style: ElevatedButton.styleFrom(backgroundColor: Colors.deepPurple),
          child: Text(showOffers ? "Request" : "Offer"),
        ),
      ),
    );
  }

  Widget _buildToggleBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ChoiceChip(
          label: Text("Offers"),
          selected: showOffers,
          onSelected: (val) => setState(() => showOffers = true),
          selectedColor: Colors.deepPurple.shade200,
        ),
        SizedBox(width: 10),
        ChoiceChip(
          label: Text("Requests"),
          selected: !showOffers,
          onSelected: (val) => setState(() => showOffers = false),
          selectedColor: Colors.deepPurple.shade200,
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      child: TextField(
        decoration: InputDecoration(
          hintText: "Search skills or names...",
          prefixIcon: Icon(Icons.search),
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          filled: true,
          fillColor: Colors.grey[100],
        ),
        onChanged: (val) => setState(() => searchQuery = val),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final filteredList = getFilteredList();

    return Scaffold(
      appBar: AppBar(
        title: Text("Skill Marketplace"),
        backgroundColor: Colors.deepPurple,
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          _buildToggleBar(),
          Expanded(
            child: ListView(
              children: filteredList.map(_buildSkillCard).toList(),
            ),
          )
        ],
      ),
    );
  }
}
