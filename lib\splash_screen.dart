import 'package:flutter/material.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with TickerProviderStateMixin {
  // Animation controllers
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late AnimationController _gradientController;

  // Animations
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _gradientAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimationSequence();
  }

  void _initializeAnimations() {
    // Fade animation for content
    _fadeController = AnimationController(duration: const Duration(milliseconds: 1500), vsync: this);
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut));

    // Scale animation for logo
    _scaleController = AnimationController(duration: const Duration(milliseconds: 1200), vsync: this);
    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut));

    // Gradient animation for background
    _gradientController = AnimationController(duration: const Duration(seconds: 3), vsync: this);
    _gradientAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _gradientController, curve: Curves.easeInOut));
  }

  void _startAnimationSequence() async {
    // Start gradient animation immediately
    _gradientController.repeat(reverse: true);

    // Delay content animations for elegance
    await Future.delayed(const Duration(milliseconds: 500));
    if (mounted) {
      _scaleController.forward();
    }

    await Future.delayed(const Duration(milliseconds: 300));
    if (mounted) {
      _fadeController.forward();
    }

    // Auto-navigate after animations complete
    await Future.delayed(const Duration(milliseconds: 2000));
    if (mounted) {
      _navigateToOnboarding();
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    _gradientController.dispose();
    super.dispose();
  }

  void _navigateToOnboarding() {
    Navigator.pushReplacementNamed(context, '/auth');
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: SizedBox(
            height: screenSize.height - MediaQuery.of(context).padding.top,
            child: AnimatedBuilder(
              animation: _gradientAnimation,
              builder: (context, child) {
                return Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors:
                          isDarkMode
                              ? [
                                Color.lerp(const Color(0xFF1A1A2E), const Color(0xFF16213E), _gradientAnimation.value)!,
                                Color.lerp(const Color(0xFF0F3460), const Color(0xFF533483), _gradientAnimation.value)!,
                              ]
                              : [
                                Color.lerp(const Color(0xFF667eea), const Color(0xFF764ba2), _gradientAnimation.value)!,
                                Color.lerp(const Color(0xFFf093fb), const Color(0xFFf5576c), _gradientAnimation.value)!,
                              ],
                    ),
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: screenSize.width * 0.08,
                      vertical: screenSize.height * 0.05,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Spacer for top section
                        const Spacer(flex: 2),

                        // Logo Section with Hero Animation
                        AnimatedBuilder(
                          animation: _scaleAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: _scaleAnimation.value,
                              child: Hero(
                                tag: 'app_logo',
                                child: Container(
                                  width: screenSize.width * 0.3,
                                  height: screenSize.width * 0.3,
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.15),
                                    borderRadius: BorderRadius.circular(30),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withValues(alpha: 0.1),
                                        blurRadius: 20,
                                        offset: const Offset(0, 10),
                                      ),
                                    ],
                                  ),
                                  child: const Icon(Icons.school_outlined, size: 80, color: Colors.white),
                                ),
                              ),
                            );
                          },
                        ),

                        SizedBox(height: screenSize.height * 0.04),

                        // App Name with Fade Animation
                        FadeTransition(
                          opacity: _fadeAnimation,
                          child: Text(
                            'IlmExchange',
                            style: TextStyle(
                              fontSize: screenSize.width * 0.08,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              letterSpacing: 1.5,
                              shadows: [
                                Shadow(
                                  color: Colors.black.withValues(alpha: 0.3),
                                  offset: const Offset(0, 2),
                                  blurRadius: 4,
                                ),
                              ],
                            ),
                          ),
                        ),

                        SizedBox(height: screenSize.height * 0.02),

                        // Tagline with Fade Animation
                        FadeTransition(
                          opacity: _fadeAnimation,
                          child: Text(
                            'Learn. Teach. Barter.',
                            style: TextStyle(
                              fontSize: screenSize.width * 0.045,
                              color: Colors.white.withValues(alpha: 0.9),
                              fontWeight: FontWeight.w300,
                              letterSpacing: 0.5,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),

                        // Spacer for middle section
                        const Spacer(flex: 3),

                        // Get Started Button with Fade Animation
                        FadeTransition(
                          opacity: _fadeAnimation,
                          child: SizedBox(
                            width: double.infinity,
                            height: 56,
                            child: ElevatedButton(
                              onPressed: _navigateToOnboarding,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.white,
                                foregroundColor: const Color(0xFF667eea),
                                elevation: 8,
                                shadowColor: Colors.black.withValues(alpha: 0.3),
                                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(28)),
                              ),
                              child: Text(
                                'Get Started',
                                style: TextStyle(
                                  fontSize: screenSize.width * 0.045,
                                  fontWeight: FontWeight.w600,
                                  letterSpacing: 0.5,
                                ),
                              ),
                            ),
                          ),
                        ),

                        // Spacer for bottom section
                        const Spacer(flex: 1),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
